"""
五个实体系统架构实现
KGC, CA, DPC, GW, MD 实体及其交互流程
"""

import hashlib
import random
import time
from typing import Dict, List, Tuple, Optional
from Paillier import PaillierCryptosystem, PaillierPublicKey, PaillierPrivateKey
from BLS import BLSShortSignature, Device, PartialSignature
from CRT import ChineseRemainderTheorem


class MemberKey:
    """成员密钥Mki"""
    
    def __init__(self, member_id: str, key_data: bytes, timestamp: float = None):
        self.member_id = member_id
        self.key_data = key_data
        self.timestamp = timestamp or time.time()
        self.signatures = {}  # device_id -> signature
        self.is_complete = False
    
    def add_signature(self, device_id: str, signature: int):
        """添加设备签名"""
        self.signatures[device_id] = signature
    
    def is_fully_signed(self, required_devices: List[str]) -> bool:
        """检查是否所有设备都已签名"""
        return all(device_id in self.signatures for device_id in required_devices)
    
    def __str__(self):
        return f"MemberKey[{self.member_id}] - 签名数: {len(self.signatures)}"


class KGC:
    """密钥生成中心 (Key Generation Center)"""
    
    def __init__(self):
        self.entity_name = "KGC"
        self.generated_keys = {}  # entity_id -> (private_key, public_key)
        self.paillier_system = PaillierCryptosystem(key_size=512)
        print(f"[{self.entity_name}] 密钥生成中心初始化完成")
    
    def generate_paillier_keypair(self, entity_id: str) -> Tuple[PaillierPrivateKey, PaillierPublicKey]:
        """为实体生成Paillier密钥对"""
        public_key, private_key = self.paillier_system.generate_keypair()
        self.generated_keys[entity_id] = (private_key, public_key)
        print(f"[{self.entity_name}] 为 {entity_id} 生成Paillier密钥对")
        return private_key, public_key
    
    def generate_bls_keypair(self, entity_id: str) -> Tuple[int, int]:
        """为实体生成BLS密钥对"""
        # 简化的BLS密钥生成
        p = 2**31 - 1
        private_key = random.randint(1, p - 1)
        public_key = pow(2, private_key, p)  # g^private_key mod p
        
        self.generated_keys[f"{entity_id}_bls"] = (private_key, public_key)
        print(f"[{self.entity_name}] 为 {entity_id} 生成BLS密钥对")
        return private_key, public_key
    
    def upload_keys_to_ca(self, ca_instance, entity_id: str):
        """将生成的密钥上传给CA"""
        if entity_id in self.generated_keys:
            keys = self.generated_keys[entity_id]
            ca_instance.receive_keys_from_kgc(entity_id, keys)
            print(f"[{self.entity_name}] 已将 {entity_id} 的密钥上传给CA")


class CA:
    """认证机构 (Certification Authority)"""
    
    def __init__(self):
        self.entity_name = "CA"
        self.registered_entities = {}  # entity_id -> keys
        self.certificates = {}  # entity_id -> certificate
        self.system_initialized = False
        print(f"[{self.entity_name}] 认证机构初始化完成")
    
    def initialize_system(self):
        """系统初始化"""
        self.system_initialized = True
        print(f"[{self.entity_name}] 系统初始化完成")
    
    def receive_keys_from_kgc(self, entity_id: str, keys: Tuple):
        """从KGC接收密钥"""
        self.registered_entities[entity_id] = keys
        # 生成证书
        certificate = self._generate_certificate(entity_id, keys)
        self.certificates[entity_id] = certificate
        print(f"[{self.entity_name}] 接收并注册 {entity_id} 的密钥，颁发证书")
    
    def _generate_certificate(self, entity_id: str, keys: Tuple) -> str:
        """生成数字证书"""
        cert_data = f"{entity_id}_{keys[1]}_{time.time()}"
        return hashlib.sha256(cert_data.encode()).hexdigest()
    
    def register_gw(self, gw_instance):
        """注册网关"""
        entity_id = "GW"
        # 为GW分发密钥
        if entity_id in self.registered_entities:
            keys = self.registered_entities[entity_id]
            gw_instance.receive_keys_from_ca(keys, self.certificates[entity_id])
            print(f"[{self.entity_name}] 已注册并分发密钥给 GW")
    
    def register_md(self, md_instance, md_id: str):
        """注册移动设备"""
        if md_id in self.registered_entities:
            keys = self.registered_entities[md_id]
            md_instance.receive_keys_from_ca(keys, self.certificates[md_id])
            print(f"[{self.entity_name}] 已注册并分发密钥给 {md_id}")


class DPC:
    """数据处理中心 (Data Processing Center)"""
    
    def __init__(self):
        self.entity_name = "DPC"
        self.aggregated_data = []
        self.member_keys = {}  # member_id -> MemberKey
        self.batch_verification_results = []
        print(f"[{self.entity_name}] 数据处理中心初始化完成")
    
    def receive_aggregated_data(self, data: bytes, signature: int, gw_id: str):
        """从GW接收聚合数据"""
        self.aggregated_data.append({
            'data': data,
            'signature': signature,
            'gw_id': gw_id,
            'timestamp': time.time()
        })
        print(f"[{self.entity_name}] 从 {gw_id} 接收聚合数据")
    
    def collect_member_key(self, member_key: MemberKey):
        """收集成员密钥Mki"""
        self.member_keys[member_key.member_id] = member_key
        print(f"[{self.entity_name}] 收集到成员密钥: {member_key}")
    
    def batch_authenticate(self) -> bool:
        """批量认证"""
        print(f"[{self.entity_name}] 开始批量认证...")
        
        # 验证聚合数据的签名
        for data_item in self.aggregated_data:
            # 简化的签名验证
            is_valid = self._verify_signature(data_item['data'], data_item['signature'])
            self.batch_verification_results.append(is_valid)
            print(f"[{self.entity_name}] 数据项验证: {'✓' if is_valid else '✗'}")
        
        # 验证成员密钥的完整性
        for mk in self.member_keys.values():
            if mk.is_complete:
                print(f"[{self.entity_name}] 成员密钥 {mk.member_id} 验证完成")
        
        all_valid = all(self.batch_verification_results)
        print(f"[{self.entity_name}] 批量认证结果: {'✓ 全部通过' if all_valid else '✗ 存在失败'}")
        return all_valid
    
    def _verify_signature(self, data: bytes, signature: int) -> bool:
        """验证签名（简化实现）"""
        # 这里应该使用实际的签名验证算法
        return True  # 简化为总是返回True
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        return {
            'aggregated_data_count': len(self.aggregated_data),
            'member_keys_count': len(self.member_keys),
            'verification_success_rate': sum(self.batch_verification_results) / len(self.batch_verification_results) if self.batch_verification_results else 0
        }


class GW:
    """网关 (Gateway)"""
    
    def __init__(self, gw_id: str = "GW_001"):
        self.entity_name = f"GW[{gw_id}]"
        self.gw_id = gw_id
        self.private_key = None
        self.public_key = None
        self.certificate = None
        self.received_data = []  # 从MD接收的数据
        self.bls_system = BLSShortSignature(n_devices=1)
        print(f"[{self.entity_name}] 网关初始化完成")
    
    def receive_keys_from_ca(self, keys: Tuple, certificate: str):
        """从CA接收密钥和证书"""
        self.private_key, self.public_key = keys
        self.certificate = certificate
        print(f"[{self.entity_name}] 从CA接收密钥和证书")
    
    def receive_data_from_md(self, encrypted_data: int, signature: int, md_id: str):
        """从MD接收加密数据和签名"""
        self.received_data.append({
            'encrypted_data': encrypted_data,
            'signature': signature,
            'md_id': md_id,
            'timestamp': time.time()
        })
        print(f"[{self.entity_name}] 从 {md_id} 接收加密数据")
    
    def aggregate_data_and_signatures(self) -> Tuple[bytes, int]:
        """聚合多维数据和签名到一个维度"""
        print(f"[{self.entity_name}] 开始聚合数据和签名...")
        
        # 聚合加密数据（Paillier同态加法）
        aggregated_encrypted_data = 0
        for data_item in self.received_data:
            aggregated_encrypted_data += data_item['encrypted_data']
        
        # 聚合签名（简化实现）
        aggregated_signature = 1
        for data_item in self.received_data:
            aggregated_signature *= data_item['signature']
        
        # 转换为字节格式
        aggregated_data_bytes = str(aggregated_encrypted_data).encode()
        
        print(f"[{self.entity_name}] 数据聚合完成，聚合了 {len(self.received_data)} 个数据项")
        return aggregated_data_bytes, aggregated_signature
    
    def upload_to_dpc(self, dpc_instance):
        """将聚合数据上传到DPC"""
        aggregated_data, aggregated_signature = self.aggregate_data_and_signatures()
        dpc_instance.receive_aggregated_data(aggregated_data, aggregated_signature, self.gw_id)
        print(f"[{self.entity_name}] 已将聚合数据上传到DPC")


class MD:
    """移动设备 (Mobile Device)"""
    
    def __init__(self, device_id: str):
        self.entity_name = f"MD[{device_id}]"
        self.device_id = device_id
        self.private_key = None
        self.public_key = None
        self.certificate = None
        self.paillier_public_key = None
        self.raw_data = random.randint(1, 100)  # 模拟原始数据
        self.encrypted_data = None
        self.bls_private_key = None
        self.bls_public_key = None
        print(f"[{self.entity_name}] 移动设备初始化完成，原始数据: {self.raw_data}")
    
    def receive_keys_from_ca(self, keys: Tuple, certificate: str):
        """从CA接收密钥和证书"""
        self.private_key, self.public_key = keys
        self.certificate = certificate
        print(f"[{self.entity_name}] 从CA接收密钥和证书")
    
    def set_paillier_public_key(self, public_key: PaillierPublicKey):
        """设置Paillier公钥"""
        self.paillier_public_key = public_key
    
    def set_bls_keys(self, private_key: int, public_key: int):
        """设置BLS密钥"""
        self.bls_private_key = private_key
        self.bls_public_key = public_key
    
    def encrypt_data(self):
        """使用Paillier同态算法加密数据"""
        if self.paillier_public_key:
            self.encrypted_data = self.paillier_public_key.encrypt(self.raw_data)
            print(f"[{self.entity_name}] 数据加密完成: {self.raw_data} -> {self.encrypted_data}")
        else:
            print(f"[{self.entity_name}] 错误: 未设置Paillier公钥")
    
    def sign_hash_curve(self, message: bytes) -> int:
        """为哈希曲线签名"""
        if self.bls_private_key:
            # 简化的BLS签名
            hash_value = int(hashlib.sha256(message).hexdigest(), 16) % (2**31 - 1)
            signature = pow(hash_value, self.bls_private_key, 2**31 - 1)
            print(f"[{self.entity_name}] 哈希曲线签名完成")
            return signature
        else:
            print(f"[{self.entity_name}] 错误: 未设置BLS私钥")
            return 0
    
    def sign_member_key(self, member_key: MemberKey) -> int:
        """用自己的私钥对成员密钥Mki进行签名"""
        if self.bls_private_key:
            # 对成员密钥数据进行签名
            hash_value = int(hashlib.sha256(member_key.key_data).hexdigest(), 16) % (2**31 - 1)
            signature = pow(hash_value, self.bls_private_key, 2**31 - 1)
            member_key.add_signature(self.device_id, signature)
            print(f"[{self.entity_name}] 对成员密钥 {member_key.member_id} 签名完成")
            return signature
        else:
            print(f"[{self.entity_name}] 错误: 未设置BLS私钥")
            return 0
    
    def send_data_to_gw(self, gw_instance):
        """发送加密数据和签名到GW"""
        if self.encrypted_data:
            message = f"data_{self.device_id}_{time.time()}".encode()
            signature = self.sign_hash_curve(message)
            gw_instance.receive_data_from_md(self.encrypted_data, signature, self.device_id)
            print(f"[{self.entity_name}] 已发送数据到GW")
        else:
            print(f"[{self.entity_name}] 错误: 数据未加密")
    
    def pass_member_key_to_next(self, next_md, member_key: MemberKey):
        """将成员密钥传递给下一个MD"""
        self.sign_member_key(member_key)
        print(f"[{self.entity_name}] 将成员密钥传递给 {next_md.entity_name}")
        return member_key


def create_member_keys(num_keys: int = 3) -> List[MemberKey]:
    """创建成员密钥"""
    member_keys = []
    for i in range(num_keys):
        member_id = f"Member_{i+1}"
        key_data = f"secret_key_data_{member_id}_{time.time()}".encode()
        member_key = MemberKey(member_id, key_data)
        member_keys.append(member_key)
    return member_keys


def demo_five_entities_system():
    """五个实体系统完整演示"""
    print("=" * 80)
    print("五个实体系统演示")
    print("KGC -> CA -> DPC, GW, MD 完整工作流程")
    print("=" * 80)
    print()

    # 第一步：初始化所有实体
    print("第一步：初始化所有实体")
    print("-" * 40)

    kgc = KGC()
    ca = CA()
    dpc = DPC()
    gw = GW("GW_001")

    # 创建多个移动设备
    md_devices = []
    for i in range(3):
        md = MD(f"MD_{i+1}")
        md_devices.append(md)

    print()

    # 第二步：KGC生成密钥并上传给CA
    print("第二步：KGC生成密钥并上传给CA")
    print("-" * 40)

    # 为GW生成密钥
    gw_paillier_keys = kgc.generate_paillier_keypair("GW")
    gw_bls_keys = kgc.generate_bls_keypair("GW")
    kgc.upload_keys_to_ca(ca, "GW")

    # 为每个MD生成密钥
    md_keys = {}
    for md in md_devices:
        paillier_keys = kgc.generate_paillier_keypair(md.device_id)
        bls_keys = kgc.generate_bls_keypair(md.device_id)
        kgc.upload_keys_to_ca(ca, md.device_id)
        md_keys[md.device_id] = {
            'paillier': paillier_keys,
            'bls': bls_keys
        }

    print()

    # 第三步：CA系统初始化并注册GW和MD
    print("第三步：CA系统初始化并注册GW和MD")
    print("-" * 40)

    ca.initialize_system()
    ca.register_gw(gw)

    # 设置GW的BLS密钥
    gw.private_key = gw_bls_keys[0]
    gw.public_key = gw_bls_keys[1]

    for md in md_devices:
        ca.register_md(md, md.device_id)
        # 设置MD的密钥
        md.set_paillier_public_key(md_keys[md.device_id]['paillier'][1])
        md.set_bls_keys(md_keys[md.device_id]['bls'][0], md_keys[md.device_id]['bls'][1])

    print()

    # 第四步：MD加密数据并签名
    print("第四步：MD加密数据并签名")
    print("-" * 40)

    for md in md_devices:
        md.encrypt_data()
        md.send_data_to_gw(gw)

    print()

    # 第五步：GW聚合数据并上传到DPC
    print("第五步：GW聚合数据并上传到DPC")
    print("-" * 40)

    gw.upload_to_dpc(dpc)

    print()

    # 第六步：成员密钥Mki的签名传递流程
    print("第六步：成员密钥Mki的签名传递流程")
    print("-" * 40)

    # 创建成员密钥
    member_keys = create_member_keys(2)

    for mk in member_keys:
        print(f"\n处理成员密钥: {mk.member_id}")

        # MD依次对成员密钥进行签名
        current_mk = mk
        for i, md in enumerate(md_devices):
            if i < len(md_devices) - 1:
                # 传递给下一个MD
                next_md = md_devices[i + 1]
                current_mk = md.pass_member_key_to_next(next_md, current_mk)
            else:
                # 最后一个MD签名后发送给DPC
                md.sign_member_key(current_mk)
                current_mk.is_complete = True
                dpc.collect_member_key(current_mk)
                print(f"[{md.entity_name}] 成员密钥签名完成，发送给DPC")

    print()

    # 第七步：DPC批量认证和数据收集
    print("第七步：DPC批量认证和数据收集")
    print("-" * 40)

    authentication_result = dpc.batch_authenticate()

    print()

    # 第八步：系统统计信息
    print("第八步：系统统计信息")
    print("-" * 40)

    stats = dpc.get_statistics()
    print(f"聚合数据数量: {stats['aggregated_data_count']}")
    print(f"成员密钥数量: {stats['member_keys_count']}")
    print(f"验证成功率: {stats['verification_success_rate']:.2%}")

    print(f"\n系统参与实体:")
    print(f"- KGC: 生成了 {len(kgc.generated_keys)} 个密钥对")
    print(f"- CA: 注册了 {len(ca.registered_entities)} 个实体")
    print(f"- GW: 聚合了 {len(gw.received_data)} 个数据项")
    print(f"- MD设备: {len(md_devices)} 个")
    print(f"- DPC: 处理了 {len(dpc.aggregated_data)} 个聚合数据和 {len(dpc.member_keys)} 个成员密钥")

    print()
    print("=" * 80)
    print("五个实体系统演示完成")
    print("=" * 80)

    return {
        'kgc': kgc,
        'ca': ca,
        'dpc': dpc,
        'gw': gw,
        'md_devices': md_devices,
        'authentication_result': authentication_result,
        'statistics': stats
    }


if __name__ == "__main__":
    # 运行完整的五个实体系统演示
    result = demo_five_entities_system()
