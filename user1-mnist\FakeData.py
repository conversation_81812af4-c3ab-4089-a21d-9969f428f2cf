import numpy as np
import torch


# 生成虚假的梯度信息
def generate_fake_gradients(model):
    # 计算模型的梯度信息
    gradients = model

    # 生成虚假的梯度信息
    fake_gradients = {}
    i = 0
    for var, grad in gradients.items():
        if i == 1:
            fake_grad = np.random.normal(loc=0.0, scale=4.0, size=grad.shape)
            fake_gradients[var] = torch.tensor(fake_grad)
        else:
            fake_gradients[var] = grad
        i += 1

    return fake_gradients


# 生成虚假的梯度信息
def generate_fake_gradients_2(model):
    # 计算模型的梯度信息
    gradients = model

    # 生成虚假的梯度信息

    fake_grad = np.random.normal(loc=0.0, scale=4.0, size=gradients.shape)
    fake_gradients = torch.tensor(fake_grad)

    return fake_gradients
