"""
中国剩余定理 (Chinese Remainder Theorem, CRT) 算法实现
解决同余方程组：
x ≡ a1 (mod m1)
x ≡ a2 (mod m2)
...
x ≡ ak (mod mk)
其中 m1, m2, ..., mk 两两互质
"""

import math
from typing import List, Tuple, Optional


class ChineseRemainderTheorem:
    """中国剩余定理算法类"""

    def __init__(self):
        self.debug = False

    def extended_gcd(self, a: int, b: int) -> Tuple[int, int, int]:
        """
        扩展欧几里得算法
        返回 (x, y, gcd) 使得 ax + by = gcd(a, b)
        """
        if b == 0:
            return 1, 0, a

        x1, y1, gcd = self.extended_gcd(b, a % b)
        x = y1
        y = x1 - (a // b) * y1

        return x, y, gcd

    def mod_inverse(self, a: int, m: int) -> Optional[int]:
        """
        计算模逆元：找到 x 使得 (a * x) ≡ 1 (mod m)
        """
        x, y, gcd = self.extended_gcd(a, m)

        if gcd != 1:
            return None  # 模逆元不存在

        return (x % m + m) % m

    def check_coprime(self, m_list: List[int]) -> bool:
        """
        检查模数列表是否两两互质
        """
        n = len(m_list)
        for i in range(n):
            for j in range(i + 1, n):
                if math.gcd(m_list[i], m_list[j]) != 1:
                    if self.debug:
                        print(f"模数 {m_list[i]} 和 {m_list[j]} 不互质，gcd = {math.gcd(m_list[i], m_list[j])}")
                    return False
        return True

    def solve_crt(self, a_list: List[int], m_list: List[int]) -> Optional[int]:
        """
        求解中国剩余定理

        参数:
        a_list: 余数列表 [a1, a2, ..., ak]
        m_list: 模数列表 [m1, m2, ..., mk]

        返回:
        解 x，如果无解则返回 None
        """
        if len(a_list) != len(m_list):
            print("余数列表和模数列表长度不匹配")
            return None

        # 检查模数是否两两互质
        if not self.check_coprime(m_list):
            print("模数不满足两两互质的条件，无法使用中国剩余定理")
            return None

        # 计算总模数 M = m1 * m2 * ... * mk
        M = 1
        for mi in m_list:
            M *= mi

        if self.debug:
            print(f"总模数 M = {M}")

        # 计算 Mi = M / mi
        Mi_list = []
        for mi in m_list:
            Mi = M // mi
            Mi_list.append(Mi)
            if self.debug:
                print(f"M{len(Mi_list)} = M / m{len(Mi_list)} = {M} / {mi} = {Mi}")

        # 计算 Mi 的模逆元 yi，使得 Mi * yi ≡ 1 (mod mi)
        yi_list = []
        for i, (Mi, mi) in enumerate(zip(Mi_list, m_list)):
            yi = self.mod_inverse(Mi, mi)
            if yi is None:
                print(f"无法计算 M{i+1} 在模 m{i+1} 下的逆元")
                return None
            yi_list.append(yi)
            if self.debug:
                print(f"y{i+1} = M{i+1}^(-1) mod m{i+1} = {Mi}^(-1) mod {mi} = {yi}")
                print(f"验证: {Mi} * {yi} mod {mi} = {(Mi * yi) % mi}")

        # 计算最终解 x = Σ(ai * Mi * yi) mod M
        x = 0
        for i, (ai, Mi, yi) in enumerate(zip(a_list, Mi_list, yi_list)):
            term = ai * Mi * yi
            x += term
            if self.debug:
                print(f"项{i+1}: a{i+1} * M{i+1} * y{i+1} = {ai} * {Mi} * {yi} = {term}")

        x = x % M

        if self.debug:
            print(f"最终解: x ≡ {x} (mod {M})")

        return x

    def verify_solution(self, x: int, a_list: List[int], m_list: List[int]) -> bool:
        """
        验证解的正确性
        """
        print("验证解的正确性:")
        all_correct = True

        for i, (ai, mi) in enumerate(zip(a_list, m_list)):
            remainder = x % mi
            is_correct = remainder == ai
            print(f"x ≡ {remainder} (mod {mi}), 期望: {ai}, {'✓' if is_correct else '✗'}")
            if not is_correct:
                all_correct = False

        return all_correct


def demo_crt():
    """中国剩余定理演示"""
    print("=== 中国剩余定理算法演示 ===\n")

    crt = ChineseRemainderTheorem()
    crt.debug = True

    # 示例1：经典问题
    print("示例1：经典中国剩余定理问题")
    print("求解同余方程组:")
    print("x ≡ 2 (mod 3)")
    print("x ≡ 3 (mod 5)")
    print("x ≡ 2 (mod 7)")
    print()

    a_list1 = [2, 3, 2]
    m_list1 = [3, 5, 7]

    solution1 = crt.solve_crt(a_list1, m_list1)
    if solution1 is not None:
        print(f"\n解: x = {solution1}")
        print()
        crt.verify_solution(solution1, a_list1, m_list1)

    print("\n" + "="*50 + "\n")

    # 示例2：更复杂的问题
    print("示例2：更复杂的同余方程组")
    print("求解同余方程组:")
    print("x ≡ 1 (mod 4)")
    print("x ≡ 2 (mod 9)")
    print("x ≡ 3 (mod 25)")
    print()

    crt.debug = False  # 关闭详细输出
    a_list2 = [1, 2, 3]
    m_list2 = [4, 9, 25]

    solution2 = crt.solve_crt(a_list2, m_list2)
    if solution2 is not None:
        print(f"解: x = {solution2}")
        print()
        crt.verify_solution(solution2, a_list2, m_list2)

    print("\n" + "="*50 + "\n")

    # 示例3：错误情况（模数不互质）
    print("示例3：模数不互质的情况")
    print("尝试求解:")
    print("x ≡ 1 (mod 6)")
    print("x ≡ 2 (mod 9)")
    print("(注意: gcd(6,9) = 3 ≠ 1)")
    print()

    a_list3 = [1, 2]
    m_list3 = [6, 9]

    solution3 = crt.solve_crt(a_list3, m_list3)
    if solution3 is None:
        print("如预期，无法求解")


def interactive_crt():
    """交互式中国剩余定理求解器"""
    print("=== 交互式中国剩余定理求解器 ===\n")

    try:
        print("请输入余数ai，以逗号分隔：")
        a_input = input().strip()
        a_list = list(map(int, a_input.split(",")))

        print("请输入模数mi，以逗号分隔：")
        m_input = input().strip()
        m_list = list(map(int, m_input.split(",")))

        print(f"\n求解同余方程组:")
        for i, (ai, mi) in enumerate(zip(a_list, m_list)):
            print(f"x ≡ {ai} (mod {mi})")
        print()

        crt = ChineseRemainderTheorem()
        solution = crt.solve_crt(a_list, m_list)

        if solution is not None:
            print(f"解: x = {solution}")
            print()
            crt.verify_solution(solution, a_list, m_list)

    except ValueError:
        print("输入格式错误，请输入整数并用逗号分隔")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == '__main__':
    # 运行演示
    demo_crt()

    print("\n" + "="*60 + "\n")

    # 交互式求解器
    interactive_crt()

