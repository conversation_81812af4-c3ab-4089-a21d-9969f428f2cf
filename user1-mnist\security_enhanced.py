"""
安全增强版本 - 防御设备渗透和私钥伪造攻击
包含多重安全机制和拜占庭容错
"""

import hashlib
import random
import time
from typing import Dict, List, Tuple, Optional
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
import hmac


class SecureKeyStorage:
    """安全密钥存储 - 使用硬件安全模块(HSM)模拟"""
    
    def __init__(self, device_id: str):
        self.device_id = device_id
        self._encrypted_keys = {}
        self._device_secret = self._generate_device_secret()
        self.access_count = 0
        self.max_access = 1000  # 访问次数限制
    
    def _generate_device_secret(self) -> bytes:
        """生成设备唯一密钥（模拟TPM/TEE）"""
        return hashlib.sha256(f"device_secret_{self.device_id}_{time.time()}".encode()).digest()
    
    def store_private_key(self, private_key: int) -> str:
        """安全存储私钥"""
        # 使用设备密钥加密私钥
        key_data = str(private_key).encode()
        encrypted_key = self._encrypt_with_device_secret(key_data)
        key_id = hashlib.sha256(key_data).hexdigest()[:16]
        self._encrypted_keys[key_id] = encrypted_key
        return key_id
    
    def retrieve_private_key(self, key_id: str) -> Optional[int]:
        """安全检索私钥"""
        if self.access_count >= self.max_access:
            raise SecurityError("密钥访问次数超限，设备可能被攻击")
        
        self.access_count += 1
        
        if key_id not in self._encrypted_keys:
            return None
        
        encrypted_key = self._encrypted_keys[key_id]
        decrypted_data = self._decrypt_with_device_secret(encrypted_key)
        return int(decrypted_data.decode())
    
    def _encrypt_with_device_secret(self, data: bytes) -> bytes:
        """使用设备密钥加密（简化实现）"""
        return hmac.new(self._device_secret, data, hashlib.sha256).digest()
    
    def _decrypt_with_device_secret(self, encrypted_data: bytes) -> bytes:
        """使用设备密钥解密（简化实现）"""
        # 在实际实现中，这里需要真正的加密/解密算法
        # 这里为了演示，使用HMAC验证
        return encrypted_data  # 简化实现


class SecurityError(Exception):
    """安全异常"""
    pass


class ByzantineFaultTolerance:
    """拜占庭容错机制"""
    
    def __init__(self, total_nodes: int):
        self.total_nodes = total_nodes
        self.max_byzantine = (total_nodes - 1) // 3  # 最多容忍f个拜占庭节点
        self.min_honest = total_nodes - self.max_byzantine
        
    def is_sufficient_signatures(self, signature_count: int) -> bool:
        """检查签名数量是否足够（需要至少2f+1个签名）"""
        required = 2 * self.max_byzantine + 1
        return signature_count >= required
    
    def detect_byzantine_behavior(self, signatures: Dict[str, int], expected_hash: int) -> List[str]:
        """检测拜占庭行为"""
        suspicious_devices = []
        
        # 统计签名值的分布
        signature_counts = {}
        for device_id, signature in signatures.items():
            if signature not in signature_counts:
                signature_counts[signature] = []
            signature_counts[signature].append(device_id)
        
        # 找出少数派（可能是拜占庭节点）
        majority_threshold = len(signatures) // 2 + 1
        for signature, devices in signature_counts.items():
            if len(devices) < majority_threshold:
                suspicious_devices.extend(devices)
        
        return suspicious_devices


class SecureCA:
    """安全增强的认证机构"""
    
    def __init__(self):
        self.entity_name = "SecureCA"
        self.registered_entities = {}
        self.revoked_certificates = set()  # 撤销列表
        self.certificate_chain = {}  # 证书链
        self.byzantine_detector = ByzantineFaultTolerance(total_nodes=5)
        
    def issue_certificate(self, entity_id: str, public_key: int) -> Dict:
        """颁发数字证书"""
        timestamp = time.time()
        cert_data = {
            'entity_id': entity_id,
            'public_key': public_key,
            'issuer': self.entity_name,
            'issued_at': timestamp,
            'expires_at': timestamp + 365 * 24 * 3600,  # 1年有效期
            'serial_number': hashlib.sha256(f"{entity_id}_{timestamp}".encode()).hexdigest()[:16]
        }
        
        # 生成证书签名
        cert_signature = self._sign_certificate(cert_data)
        cert_data['signature'] = cert_signature
        
        self.certificate_chain[entity_id] = cert_data
        return cert_data
    
    def verify_certificate(self, certificate: Dict) -> bool:
        """验证证书有效性"""
        # 检查是否在撤销列表中
        if certificate['serial_number'] in self.revoked_certificates:
            return False
        
        # 检查有效期
        if time.time() > certificate['expires_at']:
            return False
        
        # 验证证书签名
        return self._verify_certificate_signature(certificate)
    
    def revoke_certificate(self, serial_number: str):
        """撤销证书"""
        self.revoked_certificates.add(serial_number)
        print(f"[{self.entity_name}] 证书 {serial_number} 已被撤销")
    
    def _sign_certificate(self, cert_data: Dict) -> str:
        """签名证书"""
        cert_string = str(sorted(cert_data.items()))
        return hashlib.sha256(cert_string.encode()).hexdigest()
    
    def _verify_certificate_signature(self, certificate: Dict) -> bool:
        """验证证书签名"""
        cert_copy = certificate.copy()
        signature = cert_copy.pop('signature')
        expected_signature = self._sign_certificate(cert_copy)
        return signature == expected_signature


class SecureMD:
    """安全增强的移动设备"""
    
    def __init__(self, device_id: str):
        self.entity_name = f"SecureMD[{device_id}]"
        self.device_id = device_id
        self.key_storage = SecureKeyStorage(device_id)
        self.certificate = None
        self.private_key_id = None
        self.public_key = None
        self.signature_history = []  # 签名历史记录
        self.is_compromised = False  # 设备状态标记
        
    def initialize_keys(self, private_key: int, public_key: int, certificate: Dict):
        """初始化密钥和证书"""
        self.private_key_id = self.key_storage.store_private_key(private_key)
        self.public_key = public_key
        self.certificate = certificate
        print(f"[{self.entity_name}] 安全密钥初始化完成")
    
    def secure_sign(self, message: bytes) -> Tuple[int, Dict]:
        """安全签名（包含防篡改检测）"""
        try:
            # 检索私钥
            private_key = self.key_storage.retrieve_private_key(self.private_key_id)
            if private_key is None:
                raise SecurityError("无法检索私钥")
            
            # 生成签名
            hash_value = int(hashlib.sha256(message).hexdigest(), 16) % (2**31 - 1)
            signature = pow(hash_value, private_key, 2**31 - 1)
            
            # 创建签名元数据
            signature_metadata = {
                'device_id': self.device_id,
                'timestamp': time.time(),
                'message_hash': hash_value,
                'signature': signature,
                'certificate_serial': self.certificate['serial_number'] if self.certificate else None
            }
            
            # 记录签名历史
            self.signature_history.append(signature_metadata)
            
            print(f"[{self.entity_name}] 安全签名完成")
            return signature, signature_metadata
            
        except Exception as e:
            print(f"[{self.entity_name}] 签名失败: {e}")
            self.is_compromised = True
            raise SecurityError(f"设备可能被攻击: {e}")
    
    def verify_device_integrity(self) -> bool:
        """验证设备完整性"""
        # 检查访问模式异常
        if self.key_storage.access_count > 100:  # 异常高频访问
            print(f"[{self.entity_name}] 警告: 检测到异常密钥访问模式")
            return False
        
        # 检查签名一致性
        if len(self.signature_history) > 1:
            recent_signatures = self.signature_history[-5:]  # 最近5个签名
            # 简单的一致性检查
            for sig_meta in recent_signatures:
                if sig_meta['device_id'] != self.device_id:
                    print(f"[{self.entity_name}] 警告: 检测到设备ID不一致")
                    return False
        
        return True


class SecureDPC:
    """安全增强的数据处理中心"""
    
    def __init__(self):
        self.entity_name = "SecureDPC"
        self.ca = SecureCA()
        self.byzantine_detector = ByzantineFaultTolerance(total_nodes=5)
        self.signature_pool = {}  # 签名池
        self.suspicious_devices = set()  # 可疑设备列表
        
    def advanced_signature_verification(self, signatures: Dict[str, Tuple[int, Dict]], 
                                      message: bytes) -> Dict[str, bool]:
        """高级签名验证"""
        verification_results = {}
        signature_values = {}
        
        for device_id, (signature, metadata) in signatures.items():
            # 1. 证书验证
            if not self._verify_device_certificate(metadata):
                verification_results[device_id] = False
                self.suspicious_devices.add(device_id)
                continue
            
            # 2. 签名数学验证
            if not self._verify_signature_math(signature, message, metadata):
                verification_results[device_id] = False
                self.suspicious_devices.add(device_id)
                continue
            
            # 3. 时间戳验证
            if not self._verify_timestamp(metadata['timestamp']):
                verification_results[device_id] = False
                continue
            
            verification_results[device_id] = True
            signature_values[device_id] = signature
        
        # 4. 拜占庭容错检测
        if len(signature_values) > 0:
            expected_hash = int(hashlib.sha256(message).hexdigest(), 16) % (2**31 - 1)
            byzantine_devices = self.byzantine_detector.detect_byzantine_behavior(
                signature_values, expected_hash
            )
            
            for device_id in byzantine_devices:
                verification_results[device_id] = False
                self.suspicious_devices.add(device_id)
                print(f"[{self.entity_name}] 检测到拜占庭行为: {device_id}")
        
        return verification_results
    
    def _verify_device_certificate(self, metadata: Dict) -> bool:
        """验证设备证书"""
        if not metadata.get('certificate_serial'):
            return False
        return metadata['certificate_serial'] not in self.ca.revoked_certificates
    
    def _verify_signature_math(self, signature: int, message: bytes, metadata: Dict) -> bool:
        """验证签名数学正确性"""
        # 重新计算哈希
        expected_hash = int(hashlib.sha256(message).hexdigest(), 16) % (2**31 - 1)
        return expected_hash == metadata['message_hash']
    
    def _verify_timestamp(self, timestamp: float) -> bool:
        """验证时间戳（防重放攻击）"""
        current_time = time.time()
        # 允许5分钟的时间偏差
        return abs(current_time - timestamp) < 300
    
    def get_security_report(self) -> Dict:
        """生成安全报告"""
        return {
            'total_signatures_verified': len(self.signature_pool),
            'suspicious_devices': list(self.suspicious_devices),
            'revoked_certificates': len(self.ca.revoked_certificates),
            'byzantine_tolerance': f"最多容忍 {self.byzantine_detector.max_byzantine} 个拜占庭节点"
        }


def demo_security_enhanced():
    """安全增强系统演示"""
    print("=== 安全增强系统演示 ===\n")
    
    # 初始化安全组件
    secure_dpc = SecureDPC()
    
    # 创建安全MD设备
    devices = []
    for i in range(5):
        device = SecureMD(f"SecureMD_{i+1}")
        
        # 生成密钥和证书
        private_key = random.randint(1, 2**31 - 2)
        public_key = pow(2, private_key, 2**31 - 1)
        certificate = secure_dpc.ca.issue_certificate(device.device_id, public_key)
        
        device.initialize_keys(private_key, public_key, certificate)
        devices.append(device)
    
    # 模拟正常签名过程
    message = b"Secure transaction data"
    signatures = {}
    
    print("正常设备签名过程:")
    for device in devices[:3]:  # 前3个设备正常签名
        signature, metadata = device.secure_sign(message)
        signatures[device.device_id] = (signature, metadata)
    
    # 模拟攻击场景
    print("\n模拟攻击场景:")
    
    # 攻击1: 设备渗透
    compromised_device = devices[3]
    compromised_device.is_compromised = True
    print(f"设备 {compromised_device.device_id} 被攻击者渗透")
    
    # 攻击2: 私钥伪造
    fake_private_key = random.randint(1, 2**31 - 2)
    fake_signature = pow(int(hashlib.sha256(message).hexdigest(), 16) % (2**31 - 1), 
                        fake_private_key, 2**31 - 1)
    fake_metadata = {
        'device_id': 'FAKE_DEVICE',
        'timestamp': time.time(),
        'message_hash': int(hashlib.sha256(message).hexdigest(), 16) % (2**31 - 1),
        'signature': fake_signature,
        'certificate_serial': 'FAKE_CERT'
    }
    signatures['FAKE_DEVICE'] = (fake_signature, fake_metadata)
    
    # 安全验证
    print("\n安全验证结果:")
    verification_results = secure_dpc.advanced_signature_verification(signatures, message)
    
    for device_id, is_valid in verification_results.items():
        status = "✓ 有效" if is_valid else "✗ 无效/可疑"
        print(f"设备 {device_id}: {status}")
    
    # 生成安全报告
    print("\n安全报告:")
    security_report = secure_dpc.get_security_report()
    for key, value in security_report.items():
        print(f"{key}: {value}")


if __name__ == "__main__":
    demo_security_enhanced()
