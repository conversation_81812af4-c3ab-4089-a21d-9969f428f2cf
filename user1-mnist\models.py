import torch
from torch import nn
import torch.nn.functional as F
from torchvision import models

class CNN(nn.Module):
    def __init__(self, classes=10):
        super(CNN, self).__init__()
        # input = 28*28 PIL.ToTensor()
        self.conv1 = nn.Conv2d(1, 20, 5, 1)  # 20 [(1*)5*5] conv. kernels
        self.conv2 = nn.Conv2d(20, 50, 5, 1)  # 50 [(20*)5*5] conv. kernels
        self.fc1 = nn.Linear(4 * 4 * 50, 500)  # linear 1
        self.fc2 = nn.Linear(500, classes)  # linear 2

        self.init_weights(seed=1)

    def init_weights(self, seed=None):
        # common random seed is vital for the effectiveness of averaging, see fig. 1 in
        # <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>'s paper
        if seed:
            torch.manual_seed(seed)
            nn.init.xavier_uniform_(self.conv1.weight)  # current best practice
            nn.init.xavier_uniform_(self.conv2.weight)
        else:  # deprecated (likely to drop in a local-minimal trap)
            nn.init.zeros_(self.conv1.weight)
            nn.init.zeros_(self.conv2.weight)
            nn.init.zeros_(self.fc1.weight)
            nn.init.zeros_(self.fc1.bias)
            nn.init.zeros_(self.fc2.weight)
            nn.init.zeros_(self.fc2.bias)

    def forward(self, x):
        x = F.relu(self.conv1(x))
        x = F.max_pool2d(x, 2, 2)
        x = F.relu(self.conv2(x))
        x = F.max_pool2d(x, 2, 2)
        x = x.view(-1, 4 * 4 * 50)  # flatten
        x = F.relu(self.fc1(x))
        x = self.fc2(x)
        return F.log_softmax(x, dim=1)


class Reg(nn.Module):
    """
    Linear regression model implemented as a single-layer NN
    """
    def __init__(self, in_features, out_features):
        super(Reg, self).__init__()
        self.linear = nn.Linear(in_features, out_features)  # in = independent vars, out = dependent vars

        # init weights
        self.init_weights()

    def init_weights(self, seed=None):
        # common random seed is vital for the effectiveness of averaging, see fig. 1 in
        # McMahan, B.'s paper
        if seed:
            torch.manual_seed(seed)
        else:  # all zero init, deprecated (likely to drop in a local-minimal trap)
            nn.init.zeros_(self.linear.weight)
            nn.init.zeros_(self.linear.bias)



    def forward(self, x):
        y_pred = self.linear(x)  # y_pred = w * x - b
        return y_pred

class SVM(nn.Module):
    """
    Linear Support Vector Machine implemented as a single-layer NN plus regularization
    hyperplane: wx - b = 0
    + samples: wx - b > 1
    - samples: wx - b < -1
    Loss function =  ||w||/2 + C*sum{ max[0, 1 - y(wx - b)]^2 }, C is a hyper-param
    Guide: http://bytepawn.com/svm-with-pytorch.html
    """
    def __init__(self, in_features):
        super(SVM, self).__init__()
        # self.linear = torch.nn.Linear(in_features, out_features)  # in = independent vars, out = dependent vars
        #
        # # init weights
        # torch.nn.init.zeros_(self.linear.weight)
        # torch.nn.init.zeros_(self.linear.bias)
        self.w = nn.Parameter(torch.zeros(in_features), requires_grad=True)
        self.b = nn.Parameter(torch.zeros(1), requires_grad=True)

    def get_w(self):
        return self.w

    def get_b(self):
        return self.b

    def forward(self, x):
        y_hat = torch.mv(x, self.w) - self.b  # y_pred = w * x - b
        return y_hat.reshape(-1, 1)


# 各种机器学习模型
def get_model(name="CNN", pretrained=True):
    if name == "CNN":
        model = CNN()
    elif name == "Reg":
        model = Reg()
    elif name == "SVM":
        model = SVM()
    elif name == "resnet18":
        model = models.resnet18(pretrained=pretrained)
    elif name == "resnet50":
        model = models.resnet50(pretrained=pretrained)
    elif name == "densenet121":
        model = models.densenet121(pretrained=pretrained)
    elif name == "alexnet":
        model = models.alexnet(pretrained=pretrained)
    elif name == "vgg16":
        model = models.vgg16(pretrained=pretrained)
    elif name == "vgg19":
        model = models.vgg19(pretrained=pretrained)
    elif name == "inception_v3":
        model = models.inception_v3(pretrained=pretrained)
    elif name == "googlenet":
        model = models.googlenet(pretrained=pretrained)

    if torch.cuda.is_available():
        return model.cuda()
    else:
        return model
