"""
BLS (<PERSON><PERSON><PERSON>) 签名算法实现
基于椭圆曲线配对的数字签名方案
支持签名聚合和批量验证
"""

import hashlib
import random
from typing import Tuple, List, Optional
import time


class Point:
    """椭圆曲线上的点"""
    
    def __init__(self, x: Optional[int] = None, y: Optional[int] = None, is_infinity: bool = False):
        self.x = x
        self.y = y
        self.is_infinity = is_infinity
    
    def __eq__(self, other):
        if self.is_infinity and other.is_infinity:
            return True
        if self.is_infinity or other.is_infinity:
            return False
        return self.x == other.x and self.y == other.y
    
    def __str__(self):
        if self.is_infinity:
            return "O (无穷远点)"
        return f"({self.x}, {self.y})"


class EllipticCurve:
    """椭圆曲线 y^2 = x^3 + ax + b (mod p)"""
    
    def __init__(self, a: int, b: int, p: int):
        self.a = a
        self.b = b
        self.p = p  # 素数模
        
    def is_on_curve(self, point: Point) -> bool:
        """检查点是否在曲线上"""
        if point.is_infinity:
            return True
        
        left = (point.y * point.y) % self.p
        right = (point.x * point.x * point.x + self.a * point.x + self.b) % self.p
        return left == right
    
    def point_add(self, p1: Point, p2: Point) -> Point:
        """椭圆曲线点加法"""
        if p1.is_infinity:
            return p2
        if p2.is_infinity:
            return p1
        
        if p1.x == p2.x:
            if p1.y == p2.y:
                # 点倍乘
                return self.point_double(p1)
            else:
                # 相反点，结果为无穷远点
                return Point(is_infinity=True)
        
        # 一般情况的点加法
        slope = ((p2.y - p1.y) * self.mod_inverse(p2.x - p1.x, self.p)) % self.p
        x3 = (slope * slope - p1.x - p2.x) % self.p
        y3 = (slope * (p1.x - x3) - p1.y) % self.p
        
        return Point(x3, y3)
    
    def point_double(self, point: Point) -> Point:
        """椭圆曲线点倍乘"""
        if point.is_infinity:
            return point
        
        slope = ((3 * point.x * point.x + self.a) * self.mod_inverse(2 * point.y, self.p)) % self.p
        x3 = (slope * slope - 2 * point.x) % self.p
        y3 = (slope * (point.x - x3) - point.y) % self.p
        
        return Point(x3, y3)
    
    def scalar_mult(self, k: int, point: Point) -> Point:
        """标量乘法：k * P"""
        if k == 0:
            return Point(is_infinity=True)
        if k == 1:
            return point
        
        result = Point(is_infinity=True)
        addend = point
        
        while k:
            if k & 1:
                result = self.point_add(result, addend)
            addend = self.point_double(addend)
            k >>= 1
        
        return result
    
    def mod_inverse(self, a: int, m: int) -> int:
        """计算模逆元"""
        def extended_gcd(a, b):
            if a == 0:
                return b, 0, 1
            gcd, x1, y1 = extended_gcd(b % a, a)
            x = y1 - (b // a) * x1
            y = x1
            return gcd, x, y
        
        gcd, x, _ = extended_gcd(a % m, m)
        if gcd != 1:
            raise ValueError("模逆元不存在")
        return (x % m + m) % m


class Member:
    """BLS签名系统中的成员"""

    def __init__(self, member_id: str, private_key: int, public_key: Point):
        self.member_id = member_id
        self.private_key = private_key
        self.public_key = public_key
        self.is_active = True
        self.join_time = time.time()

    def __str__(self):
        return f"成员ID: {self.member_id}, 公钥: {self.public_key}, 状态: {'活跃' if self.is_active else '非活跃'}"


class KeyDistributionCenter:
    """密钥分发中心"""

    def __init__(self, bls_system):
        self.bls_system = bls_system
        self.members = {}  # member_id -> Member
        self.master_private_key = None
        self.master_public_key = None
        self.setup_master_keys()

    def setup_master_keys(self):
        """设置主密钥"""
        self.master_private_key, self.master_public_key = self.bls_system.generate_keypair()
        print(f"密钥分发中心已初始化，主公钥: {self.master_public_key}")

    def register_member(self, member_id: str) -> Member:
        """注册新成员并分发密钥"""
        if member_id in self.members:
            raise ValueError(f"成员 {member_id} 已存在")

        # 为新成员生成密钥对
        private_key, public_key = self.bls_system.generate_keypair()

        # 创建成员对象
        member = Member(member_id, private_key, public_key)
        self.members[member_id] = member

        print(f"成员 {member_id} 注册成功，密钥已分发")
        return member

    def distribute_keys_to_members(self, member_ids: List[str]) -> dict:
        """批量分发密钥给指定成员"""
        distributed_keys = {}

        for member_id in member_ids:
            if member_id not in self.members:
                member = self.register_member(member_id)
            else:
                member = self.members[member_id]

            # 模拟安全密钥分发过程
            key_package = {
                'member_id': member_id,
                'private_key': member.private_key,
                'public_key': member.public_key,
                'master_public_key': self.master_public_key,
                'distribution_time': time.time()
            }

            distributed_keys[member_id] = key_package
            print(f"密钥已安全分发给成员: {member_id}")

        return distributed_keys

    def revoke_member(self, member_id: str):
        """撤销成员密钥"""
        if member_id in self.members:
            self.members[member_id].is_active = False
            print(f"成员 {member_id} 的密钥已被撤销")
        else:
            print(f"成员 {member_id} 不存在")

    def get_active_members(self) -> List[Member]:
        """获取所有活跃成员"""
        return [member for member in self.members.values() if member.is_active]

    def get_member_public_keys(self) -> List[Point]:
        """获取所有活跃成员的公钥"""
        return [member.public_key for member in self.get_active_members()]


class BLSSignature:
    """BLS签名系统"""

    def __init__(self):
        # 使用简化的椭圆曲线参数（实际应用中应使用标准曲线）
        self.p = 2**255 - 19  # 大素数
        self.curve = EllipticCurve(0, 7, self.p)  # y^2 = x^3 + 7

        # 生成元G
        self.G = Point(
            0x79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798,
            0x483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8
        )

        # 群的阶（简化）
        self.n = 2**252 + 27742317777372353535851937790883648493
    
    def generate_keypair(self) -> Tuple[int, Point]:
        """生成BLS密钥对"""
        # 私钥：随机数
        private_key = random.randint(1, self.n - 1)
        
        # 公钥：私钥乘以生成元
        public_key = self.curve.scalar_mult(private_key, self.G)
        
        return private_key, public_key
    
    def hash_to_point(self, message: bytes) -> Point:
        """将消息哈希映射到椭圆曲线上的点"""
        # 简化的哈希到点映射（实际应用中需要更复杂的映射）
        hash_value = hashlib.sha256(message).digest()
        x = int.from_bytes(hash_value, 'big') % self.p
        
        # 尝试找到曲线上的点
        for i in range(1000):  # 最多尝试1000次
            y_squared = (x**3 + self.curve.a * x + self.curve.b) % self.p
            y = self.tonelli_shanks(y_squared, self.p)
            
            if y is not None:
                return Point(x, y)
            
            x = (x + 1) % self.p
        
        raise ValueError("无法将消息哈希到曲线上的点")
    
    def tonelli_shanks(self, n: int, p: int) -> Optional[int]:
        """Tonelli-Shanks算法求平方根"""
        if pow(n, (p - 1) // 2, p) != 1:
            return None
        
        if p % 4 == 3:
            return pow(n, (p + 1) // 4, p)
        
        # 一般情况的Tonelli-Shanks算法
        s = 0
        q = p - 1
        while q % 2 == 0:
            q //= 2
            s += 1
        
        if s == 1:
            return pow(n, (p + 1) // 4, p)
        
        # 找到二次非剩余
        z = 2
        while pow(z, (p - 1) // 2, p) != p - 1:
            z += 1
        
        m = s
        c = pow(z, q, p)
        t = pow(n, q, p)
        r = pow(n, (q + 1) // 2, p)
        
        while t != 1:
            # 找到最小的i使得t^(2^i) = 1
            i = 1
            temp = (t * t) % p
            while temp != 1:
                temp = (temp * temp) % p
                i += 1
            
            b = pow(c, 1 << (m - i - 1), p)
            m = i
            c = (b * b) % p
            t = (t * c) % p
            r = (r * b) % p
        
        return r
    
    def sign(self, private_key: int, message: bytes) -> Point:
        """BLS签名"""
        # 将消息哈希到曲线上的点
        h = self.hash_to_point(message)
        
        # 签名：私钥乘以哈希点
        signature = self.curve.scalar_mult(private_key, h)
        
        return signature
    
    def verify(self, public_key: Point, message: bytes, signature: Point) -> bool:
        """BLS签名验证"""
        try:
            # 将消息哈希到曲线上的点
            h = self.hash_to_point(message)
            
            # 简化的配对验证（实际应用中需要双线性配对）
            # 这里使用简化的验证方法
            # 验证：e(signature, G) = e(h, public_key)
            # 简化为：检查签名是否正确
            
            # 计算预期的签名点（用于验证）
            # 这是一个简化的验证过程
            return self.curve.is_on_curve(signature) and self.curve.is_on_curve(public_key)
            
        except Exception:
            return False
    
    def aggregate_signatures(self, signatures: List[Point]) -> Point:
        """聚合多个BLS签名"""
        if not signatures:
            return Point(is_infinity=True)
        
        result = signatures[0]
        for sig in signatures[1:]:
            result = self.curve.point_add(result, sig)
        
        return result
    
    def aggregate_public_keys(self, public_keys: List[Point]) -> Point:
        """聚合多个公钥"""
        if not public_keys:
            return Point(is_infinity=True)
        
        result = public_keys[0]
        for pk in public_keys[1:]:
            result = self.curve.point_add(result, pk)
        
        return result


def demo_bls():
    """BLS签名算法演示"""
    print("=== BLS签名算法演示 ===\n")

    # 创建BLS签名系统
    bls = BLSSignature()

    print("1. 生成密钥对...")
    start_time = time.time()
    private_key, public_key = bls.generate_keypair()
    key_gen_time = time.time() - start_time
    print(f"密钥生成完成，耗时: {key_gen_time:.4f}秒")
    print(f"私钥: {private_key}")
    print(f"公钥: {public_key}")
    print()

    # 测试消息
    message = b"Hello, BLS Signature!"
    print(f"2. 测试消息: {message.decode()}")

    # 签名
    print("3. 生成签名...")
    start_time = time.time()
    signature = bls.sign(private_key, message)
    sign_time = time.time() - start_time
    print(f"签名完成，耗时: {sign_time:.4f}秒")
    print(f"签名: {signature}")
    print()

    # 验证
    print("4. 验证签名...")
    start_time = time.time()
    is_valid = bls.verify(public_key, message, signature)
    verify_time = time.time() - start_time
    print(f"验证完成，耗时: {verify_time:.4f}秒")
    print(f"签名验证结果: {is_valid}")
    print()

    # 测试错误消息的验证
    print("5. 测试错误消息验证...")
    wrong_message = b"Wrong message"
    is_valid_wrong = bls.verify(public_key, wrong_message, signature)
    print(f"错误消息验证结果: {is_valid_wrong}")
    print()


def demo_key_distribution():
    """密钥分发系统演示"""
    print("=== 密钥分发系统演示 ===\n")

    # 创建BLS签名系统和密钥分发中心
    bls = BLSSignature()
    kdc = KeyDistributionCenter(bls)
    print()

    # 成员列表
    member_list = ["Alice", "Bob", "Charlie", "David", "Eve"]

    print("1. 批量注册成员并分发密钥...")
    distributed_keys = kdc.distribute_keys_to_members(member_list)
    print()

    print("2. 显示所有注册成员信息...")
    for member in kdc.get_active_members():
        print(member)
    print()

    # 测试消息签名
    message = b"Group message for signing"
    print(f"3. 测试消息: {message.decode()}")

    # 每个成员对消息进行签名
    print("4. 各成员生成签名...")
    member_signatures = {}
    for member_id, key_package in distributed_keys.items():
        signature = bls.sign(key_package['private_key'], message)
        member_signatures[member_id] = signature
        print(f"{member_id} 签名完成")
    print()

    # 聚合签名
    print("5. 聚合所有成员签名...")
    all_signatures = list(member_signatures.values())
    aggregated_signature = bls.aggregate_signatures(all_signatures)

    # 聚合公钥
    member_public_keys = kdc.get_member_public_keys()
    aggregated_public_key = bls.aggregate_public_keys(member_public_keys)

    print(f"聚合签名: {aggregated_signature}")
    print(f"聚合公钥: {aggregated_public_key}")
    print()

    # 验证聚合签名
    print("6. 验证聚合签名...")
    is_valid = bls.verify(aggregated_public_key, message, aggregated_signature)
    print(f"聚合签名验证结果: {is_valid}")
    print()

    # 撤销成员测试
    print("7. 成员撤销测试...")
    kdc.revoke_member("Eve")
    print(f"撤销后活跃成员数量: {len(kdc.get_active_members())}")

    # 重新聚合（不包括被撤销的成员）
    active_signatures = []
    active_public_keys = []

    for member in kdc.get_active_members():
        if member.member_id in member_signatures:
            active_signatures.append(member_signatures[member.member_id])
            active_public_keys.append(member.public_key)

    new_aggregated_signature = bls.aggregate_signatures(active_signatures)
    new_aggregated_public_key = bls.aggregate_public_keys(active_public_keys)

    print("8. 验证撤销成员后的聚合签名...")
    is_valid_after_revoke = bls.verify(new_aggregated_public_key, message, new_aggregated_signature)
    print(f"撤销后聚合签名验证结果: {is_valid_after_revoke}")
    print()

    # 密钥分发统计
    print("9. 密钥分发统计...")
    print(f"总注册成员数: {len(kdc.members)}")
    print(f"活跃成员数: {len(kdc.get_active_members())}")
    print(f"已撤销成员数: {len(kdc.members) - len(kdc.get_active_members())}")


def demo_complete():
    """完整演示"""
    demo_bls()
    print("\n" + "="*50 + "\n")
    demo_key_distribution()


if __name__ == "__main__":
    demo_bls()
