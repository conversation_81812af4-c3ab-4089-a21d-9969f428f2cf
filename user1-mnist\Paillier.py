"""
Paillier同态加密算法实现
支持加法同态性质：E(m1) * E(m2) = E(m1 + m2)
支持标量乘法：E(m)^k = E(k * m)
"""

import random
import math
from typing import Tuple, Optional
import time


class PaillierPublicKey:
    """Paillier公钥类"""

    def __init__(self, n: int):
        self.n = n  # n = p * q
        self.n_sq = n * n  # n^2
        self.g = n + 1  # 生成元，通常选择 g = n + 1

    def encrypt(self, plaintext: int, r: Optional[int] = None) -> int:
        """
        加密函数
        c = g^m * r^n mod n^2
        """
        if r is None:
            r = self._generate_random_r()

        # 确保明文在有效范围内
        if plaintext < 0:
            plaintext = self.n + plaintext

        # c = g^m * r^n mod n^2
        gm = pow(self.g, plaintext, self.n_sq)
        rn = pow(r, self.n, self.n_sq)
        ciphertext = (gm * rn) % self.n_sq

        return ciphertext

    def _generate_random_r(self) -> int:
        """生成随机数r，满足gcd(r, n) = 1"""
        while True:
            r = random.randint(1, self.n - 1)
            if math.gcd(r, self.n) == 1:
                return r

    def add_encrypted(self, c1: int, c2: int) -> int:
        """
        密文加法：E(m1) * E(m2) = E(m1 + m2)
        """
        return (c1 * c2) % self.n_sq

    def multiply_scalar(self, ciphertext: int, scalar: int) -> int:
        """
        标量乘法：E(m)^k = E(k * m)
        """
        return pow(ciphertext, scalar, self.n_sq)


class PaillierPrivateKey:
    """Paillier私钥类"""

    def __init__(self, p: int, q: int, public_key: PaillierPublicKey):
        self.p = p
        self.q = q
        self.public_key = public_key
        self.n = public_key.n
        self.n_sq = public_key.n_sq

        # 计算λ = lcm(p-1, q-1)
        self.lambda_n = self._lcm(p - 1, q - 1)

        # 计算μ = (L(g^λ mod n^2))^(-1) mod n
        # 其中 L(x) = (x-1)/n
        self.mu = self._compute_mu()

    def _lcm(self, a: int, b: int) -> int:
        """计算最小公倍数"""
        return abs(a * b) // math.gcd(a, b)

    def _l_function(self, x: int) -> int:
        """L函数：L(x) = (x-1)/n"""
        return (x - 1) // self.n

    def _compute_mu(self) -> int:
        """计算μ"""
        g_lambda = pow(self.public_key.g, self.lambda_n, self.n_sq)
        l_result = self._l_function(g_lambda)
        return self._mod_inverse(l_result, self.n)

    def _mod_inverse(self, a: int, m: int) -> int:
        """计算模逆元"""
        def extended_gcd(a, b):
            if a == 0:
                return b, 0, 1
            gcd, x1, y1 = extended_gcd(b % a, a)
            x = y1 - (b // a) * x1
            y = x1
            return gcd, x, y

        gcd, x, _ = extended_gcd(a % m, m)
        if gcd != 1:
            raise ValueError("模逆元不存在")
        return (x % m + m) % m

    def decrypt(self, ciphertext: int) -> int:
        """
        解密函数
        m = L(c^λ mod n^2) * μ mod n
        """
        c_lambda = pow(ciphertext, self.lambda_n, self.n_sq)
        l_result = self._l_function(c_lambda)
        plaintext = (l_result * self.mu) % self.n

        # 处理负数
        if plaintext > self.n // 2:
            plaintext = plaintext - self.n

        return plaintext


class PaillierCryptosystem:
    """Paillier密码系统主类"""

    def __init__(self, key_size: int = 1024):
        self.key_size = key_size
        self.public_key = None
        self.private_key = None

    def generate_keypair(self) -> Tuple[PaillierPublicKey, PaillierPrivateKey]:
        """生成密钥对"""
        # 生成两个大素数p和q
        p = self._generate_prime(self.key_size // 2)
        q = self._generate_prime(self.key_size // 2)

        # 确保p != q
        while p == q:
            q = self._generate_prime(self.key_size // 2)

        # 计算n = p * q
        n = p * q

        # 创建公钥和私钥
        public_key = PaillierPublicKey(n)
        private_key = PaillierPrivateKey(p, q, public_key)

        self.public_key = public_key
        self.private_key = private_key

        return public_key, private_key

    def _generate_prime(self, bits: int) -> int:
        """生成指定位数的素数"""
        while True:
            # 生成随机奇数
            candidate = random.getrandbits(bits)
            candidate |= (1 << bits - 1) | 1  # 确保是奇数且最高位为1

            if self._is_prime(candidate):
                return candidate

    def _is_prime(self, n: int, k: int = 10) -> bool:
        """Miller-Rabin素性测试"""
        if n < 2:
            return False
        if n == 2 or n == 3:
            return True
        if n % 2 == 0:
            return False

        # 将n-1写成d * 2^r的形式
        r = 0
        d = n - 1
        while d % 2 == 0:
            r += 1
            d //= 2

        # 进行k轮测试
        for _ in range(k):
            a = random.randrange(2, n - 1)
            x = pow(a, d, n)

            if x == 1 or x == n - 1:
                continue

            for _ in range(r - 1):
                x = pow(x, 2, n)
                if x == n - 1:
                    break
            else:
                return False

        return True


def demo_paillier():
    """Paillier算法演示"""
    print("=== Paillier同态加密算法演示 ===\n")

    # 创建Paillier密码系统
    paillier = PaillierCryptosystem(key_size=512)  # 使用较小的密钥长度用于演示

    print("1. 生成密钥对...")
    start_time = time.time()
    public_key, private_key = paillier.generate_keypair()
    key_gen_time = time.time() - start_time
    print(f"密钥生成完成，耗时: {key_gen_time:.4f}秒")
    print(f"密钥长度: {paillier.key_size}位")
    print(f"n = {public_key.n}")
    print()

    # 测试数据
    m1, m2 = 15, 25
    print(f"2. 测试数据: m1 = {m1}, m2 = {m2}")

    # 加密
    print("3. 加密操作...")
    start_time = time.time()
    c1 = public_key.encrypt(m1)
    c2 = public_key.encrypt(m2)
    encrypt_time = time.time() - start_time
    print(f"加密完成，耗时: {encrypt_time:.4f}秒")
    print(f"E({m1}) = {c1}")
    print(f"E({m2}) = {c2}")
    print()

    # 解密验证
    print("4. 解密验证...")
    start_time = time.time()
    d1 = private_key.decrypt(c1)
    d2 = private_key.decrypt(c2)
    decrypt_time = time.time() - start_time
    print(f"解密完成，耗时: {decrypt_time:.4f}秒")
    print(f"D(E({m1})) = {d1}")
    print(f"D(E({m2})) = {d2}")
    print(f"解密正确性: {d1 == m1 and d2 == m2}")
    print()

    # 同态加法
    print("5. 同态加法测试...")
    c_sum = public_key.add_encrypted(c1, c2)
    d_sum = private_key.decrypt(c_sum)
    print(f"E({m1}) * E({m2}) = E({m1} + {m2})")
    print(f"密文相乘结果解密: {d_sum}")
    print(f"明文相加结果: {m1 + m2}")
    print(f"同态加法正确性: {d_sum == m1 + m2}")
    print()

    # 标量乘法
    print("6. 标量乘法测试...")
    scalar = 3
    c_mul = public_key.multiply_scalar(c1, scalar)
    d_mul = private_key.decrypt(c_mul)
    print(f"E({m1})^{scalar} = E({m1} * {scalar})")
    print(f"密文幂运算结果解密: {d_mul}")
    print(f"明文乘法结果: {m1 * scalar}")
    print(f"标量乘法正确性: {d_mul == m1 * scalar}")
    print()

    # 负数测试
    print("7. 负数加密测试...")
    m_neg = -10
    c_neg = public_key.encrypt(m_neg)
    d_neg = private_key.decrypt(c_neg)
    print(f"原始负数: {m_neg}")
    print(f"解密结果: {d_neg}")
    print(f"负数加密正确性: {d_neg == m_neg}")


if __name__ == "__main__":
    demo_paillier()

