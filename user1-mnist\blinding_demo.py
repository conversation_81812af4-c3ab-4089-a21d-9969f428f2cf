"""
盲化因子详细演示
展示盲化因子在不同密码学场景中的应用和作用
"""

import random
import hashlib
import time
from typing import Tuple, List
from Paillier import PaillierCryptosystem


class BlindingFactorDemo:
    """盲化因子演示类"""
    
    def __init__(self):
        self.paillier_system = PaillierCryptosystem(key_size=512)
        self.public_key, self.private_key = self.paillier_system.generate_keypair()
    
    def demo_basic_blinding(self):
        """基础盲化演示"""
        print("=== 基础盲化因子演示 ===\n")
        
        # 原始明文
        original_message = 42
        print(f"原始明文: {original_message}")
        
        # 1. 不使用盲化的加密
        print("\n1. 不使用盲化的加密:")
        ciphertext_1 = self.public_key.encrypt(original_message)
        ciphertext_2 = self.public_key.encrypt(original_message)
        
        print(f"第一次加密: {ciphertext_1}")
        print(f"第二次加密: {ciphertext_2}")
        print(f"两次加密结果相同: {ciphertext_1 == ciphertext_2}")
        print("❌ 问题: 确定性加密，相同明文产生相同密文")
        
        # 2. 使用盲化因子的加密
        print("\n2. 使用盲化因子的加密:")
        blinded_ct_1 = self._encrypt_with_blinding(original_message)
        blinded_ct_2 = self._encrypt_with_blinding(original_message)
        
        print(f"第一次盲化加密: {blinded_ct_1}")
        print(f"第二次盲化加密: {blinded_ct_2}")
        print(f"两次加密结果相同: {blinded_ct_1 == blinded_ct_2}")
        print("✅ 改进: 随机化加密，相同明文产生不同密文")
        
        # 验证解密正确性
        decrypted_1 = self.private_key.decrypt(blinded_ct_1)
        decrypted_2 = self.private_key.decrypt(blinded_ct_2)
        print(f"\n解密验证:")
        print(f"第一次解密: {decrypted_1}")
        print(f"第二次解密: {decrypted_2}")
        print(f"解密正确: {decrypted_1 == decrypted_2 == original_message}")
    
    def _encrypt_with_blinding(self, plaintext: int) -> int:
        """使用盲化因子的Paillier加密"""
        # 生成随机盲化因子
        blinding_factor = random.randint(1, self.public_key.n - 1)
        
        # 基础加密
        base_ciphertext = self.public_key.encrypt(plaintext)
        
        # 添加盲化：E(m) * E(0)^r = E(m + 0*r) = E(m)
        # 但密文形式不同，增加随机性
        zero_ciphertext = self.public_key.encrypt(0, blinding_factor)
        blinded_ciphertext = self.public_key.add_encrypted(base_ciphertext, zero_ciphertext)
        
        return blinded_ciphertext
    
    def demo_blinding_attacks_prevention(self):
        """演示盲化因子如何防止攻击"""
        print("\n=== 盲化因子防攻击演示 ===\n")
        
        # 模拟攻击场景
        sensitive_data = [100, 200, 300]  # 敏感数据
        
        print("攻击场景: 攻击者试图通过密文分析推断明文")
        
        # 1. 无盲化的加密（容易被攻击）
        print("\n1. 无盲化加密 (易受攻击):")
        unblinded_ciphertexts = []
        for data in sensitive_data:
            ct = self.public_key.encrypt(data)
            unblinded_ciphertexts.append(ct)
            print(f"明文 {data} -> 密文 {ct}")
        
        # 攻击者可以进行密文分析
        print("\n攻击者分析:")
        print("- 相同明文总是产生相同密文")
        print("- 可以建立明文-密文对应表")
        print("- 通过频率分析推断数据分布")
        
        # 2. 使用盲化的加密（抗攻击）
        print("\n2. 盲化加密 (抗攻击):")
        blinded_ciphertexts = []
        for data in sensitive_data:
            ct = self._encrypt_with_blinding(data)
            blinded_ciphertexts.append(ct)
            print(f"明文 {data} -> 盲化密文 {ct}")
        
        # 再次加密相同数据
        print("\n再次加密相同数据:")
        for data in sensitive_data:
            ct = self._encrypt_with_blinding(data)
            print(f"明文 {data} -> 新盲化密文 {ct}")
        
        print("\n防护效果:")
        print("✅ 相同明文产生不同密文")
        print("✅ 攻击者无法建立固定对应关系")
        print("✅ 频率分析失效")
    
    def demo_blinding_in_protocols(self):
        """演示盲化因子在协议中的应用"""
        print("\n=== 盲化因子在协议中的应用 ===\n")
        
        # 场景：盲签名协议
        print("场景: 盲签名协议 (类似匿名投票)")
        
        # 1. 用户有一个消息要签名
        message = "我的投票选择"
        message_hash = int(hashlib.sha256(message.encode()).hexdigest(), 16) % (2**31 - 1)
        print(f"原始消息: {message}")
        print(f"消息哈希: {message_hash}")
        
        # 2. 用户生成盲化因子
        blinding_factor = random.randint(1, 2**30)
        print(f"盲化因子: {blinding_factor}")
        
        # 3. 用户盲化消息
        # 简化的盲化: blinded_msg = msg * r^e mod n
        e = 65537  # 公钥指数
        n = self.public_key.n
        blinded_message = (message_hash * pow(blinding_factor, e, n)) % n
        print(f"盲化后消息: {blinded_message}")
        
        # 4. 签名者对盲化消息签名（不知道原始内容）
        print("\n签名者视角:")
        print("- 只看到盲化后的消息")
        print("- 不知道原始消息内容")
        print("- 提供盲签名")
        
        # 简化的签名过程
        d = 123456  # 私钥指数（简化）
        blind_signature = pow(blinded_message, d, n)
        print(f"盲签名: {blind_signature}")
        
        # 5. 用户去盲化得到真实签名
        # unblind: signature = blind_sig * r^(-1) mod n
        r_inv = pow(blinding_factor, -1, n)
        real_signature = (blind_signature * r_inv) % n
        print(f"\n去盲化后的真实签名: {real_signature}")
        
        print("\n盲签名的优势:")
        print("✅ 签名者无法知道签名内容")
        print("✅ 用户获得有效签名")
        print("✅ 保护隐私的同时确保认证")


class AdvancedBlindingTechniques:
    """高级盲化技术"""
    
    def __init__(self):
        self.paillier_system = PaillierCryptosystem(key_size=512)
        self.public_key, self.private_key = self.paillier_system.generate_keypair()
    
    def additive_blinding(self, plaintext: int, noise_level: int = 100) -> Tuple[int, int]:
        """加法盲化：添加随机噪声"""
        # 生成随机噪声
        noise = random.randint(-noise_level, noise_level)
        
        # 盲化明文
        blinded_plaintext = plaintext + noise
        
        # 加密盲化后的明文
        ciphertext = self.public_key.encrypt(blinded_plaintext)
        
        return ciphertext, noise
    
    def multiplicative_blinding(self, plaintext: int) -> Tuple[int, int]:
        """乘法盲化：乘以随机因子"""
        # 生成随机因子
        blinding_factor = random.randint(2, 100)
        
        # 盲化明文
        blinded_plaintext = plaintext * blinding_factor
        
        # 加密盲化后的明文
        ciphertext = self.public_key.encrypt(blinded_plaintext)
        
        return ciphertext, blinding_factor
    
    def demo_advanced_blinding(self):
        """高级盲化技术演示"""
        print("\n=== 高级盲化技术演示 ===\n")
        
        original_value = 50
        print(f"原始值: {original_value}")
        
        # 1. 加法盲化
        print("\n1. 加法盲化:")
        add_ct, noise = self.additive_blinding(original_value)
        print(f"添加噪声: {noise}")
        print(f"盲化密文: {add_ct}")
        
        # 解密并去噪
        decrypted_with_noise = self.private_key.decrypt(add_ct)
        recovered_value = decrypted_with_noise - noise
        print(f"解密+去噪: {decrypted_with_noise} - {noise} = {recovered_value}")
        print(f"恢复正确: {recovered_value == original_value}")
        
        # 2. 乘法盲化
        print("\n2. 乘法盲化:")
        mul_ct, factor = self.multiplicative_blinding(original_value)
        print(f"乘法因子: {factor}")
        print(f"盲化密文: {mul_ct}")
        
        # 解密并去盲化
        decrypted_with_factor = self.private_key.decrypt(mul_ct)
        recovered_value = decrypted_with_factor // factor
        print(f"解密+去盲化: {decrypted_with_factor} / {factor} = {recovered_value}")
        print(f"恢复正确: {recovered_value == original_value}")


def main():
    """主演示函数"""
    print("🎭 盲化因子完整演示\n")
    
    # 基础盲化演示
    basic_demo = BlindingFactorDemo()
    basic_demo.demo_basic_blinding()
    basic_demo.demo_blinding_attacks_prevention()
    basic_demo.demo_blinding_in_protocols()
    
    # 高级盲化技术
    advanced_demo = AdvancedBlindingTechniques()
    advanced_demo.demo_advanced_blinding()
    
    print("\n" + "="*60)
    print("盲化因子总结:")
    print("✅ 提供语义安全性 - 相同明文产生不同密文")
    print("✅ 防止确定性攻击 - 增加随机性")
    print("✅ 保护隐私 - 隐藏真实数据模式")
    print("✅ 支持盲签名 - 在不泄露内容的情况下获得签名")
    print("✅ 增强协议安全 - 防止各种密码学攻击")
    print("="*60)


if __name__ == "__main__":
    main()
