from ctypes import sizeof

from torch import ne

import models
import torch
import tensorflow as tf
import numpy as np
from scipy.optimize import curve_fit
import math
import FakeData



# 客户端类
class Client(object):
    # 构造函数
    def __init__(self, conf, model, train_dataset, id=-1):
        # 读取配置文件
        self.conf = conf
        # 根据配置文件获取客户端本地模型（一般由服务器传输）
        self.local_model = models.get_model(self.conf["model_name"])
        # 客户端ID
        self.client_id = id
        # 客户端本地数据集
        self.train_dataset = train_dataset
        # 按ID对数据集集合进行拆分
        all_range = list(range(len(self.train_dataset)))
        data_len = int(len(self.train_dataset) / self.conf['no_models'])
        train_indices = all_range[id * data_len: (id + 1) * data_len]
        # 生成数据加载器
        self.train_loader = torch.utils.data.DataLoader(
            # 指定父集合
            self.train_dataset,
            # 每个batch加载多少样本
            batch_size=conf["batch_size"],
            # 指定子集合
            # sampler定义从数据集中提取样本的策略
            sampler=torch.utils.data.sampler.SubsetRandomSampler(train_indices)
        )

    # 模型本地训练函数
    def local_train(self, model):
        # 客户端获取服务器的模型，然后通过部分本地数据集进行训练
        for name, param in model.state_dict().items():
            # 用服务器下发的全局模型覆盖本地模型
            self.local_model.state_dict()[name].copy_(param.clone())
        # 定义最优化函数器用户本地模型训练
        optimizer = torch.optim.SGD(
            self.local_model.parameters(),
            lr=self.conf['lr'],
            momentum=self.conf['momentum']
        )
        # 本地训练模型
        # 设置开启模型训练
        self.local_model.train()
        # 开始训练模型
        for e in range(self.conf["local_epochs"]):
            for batch_id, batch in enumerate(self.train_loader):
                data, target = batch
                # 如果可以的话加载到gpu
                if torch.cuda.is_available():
                    data = data.cuda()
                    target = target.cuda()
                # 梯度初始化为0
                optimizer.zero_grad()
                # 训练预测
                output = self.local_model(data)
                # 计算损失函数cross_entropy交叉熵误差
                loss = torch.nn.functional.cross_entropy(output, target)
                # 反向传播
                loss.backward()
                # 更新参数
                optimizer.step()
            # print("Epoch %d done." % e)
        # 创建差值字典（结构与模型参数同规格），用于记录差值
        diff = dict()
        sign1 = dict()
        sign2 = dict()
        sign3 = dict()
        sign5 = dict()
        for name, data in self.local_model.state_dict().items():
            # 计算训练后与训练前的差值
            # sign4 = torch.zeros_like(data)
            # print(data)
            # print(model.state_dict()[name])
            # sign1 = data.gt(0)
            # sign2 = model.state_dict()[name].gt(0)
            # sign3 = sign1.eq(sign2)
            # print(sign1)
            # print(sign2)
            # print(sign3)
            # diff[name] = (data - model.state_dict()[name])
            # sign5 = torch.where(sign3, diff[name], sign4)
            # diff[name] = sign5
            # print(sign5)
            # print("Client %d local train done" % self.client_id)
            d = -1
            diff[name] = (data - model.state_dict()[name])

        # 客户端返回差值
        return diff

    # 模型本地训练函数
    def local_train_fake(self, model):
        # 客户端获取服务器的模型，然后通过部分本地数据集进行训练
        for name, param in model.state_dict().items():
            # 用服务器下发的全局模型覆盖本地模型
            self.local_model.state_dict()[name].copy_(param.clone())
        # 定义最优化函数器用户本地模型训练
        optimizer = torch.optim.SGD(
            self.local_model.parameters(),
            lr=self.conf['lr'],
            momentum=self.conf['momentum']
        )
        # 本地训练模型
        # 设置开启模型训练
        self.local_model.train()
        # 开始训练模型
        for e in range(self.conf["local_epochs"]):
            for batch_id, batch in enumerate(self.train_loader):
                data, target = batch
                # 如果可以的话加载到gpu
                if torch.cuda.is_available():
                    data = data.cuda()
                    target = target.cuda()
                # 梯度初始化为0
                optimizer.zero_grad()
                # 训练预测
                output = self.local_model(data)
                # 计算损失函数cross_entropy交叉熵误差
                loss = torch.nn.functional.cross_entropy(output, target)
                # 反向传播
                loss.backward()
                # 更新参数
                optimizer.step()
            # print("Epoch %d done." % e)
        # 创建差值字典（结构与模型参数同规格），用于记录差值
        diff = {}
        diff_fake = {}
        sign1 = dict()
        sign2 = dict()
        sign3 = dict()
        sign6 = {}
        for name, params in self.local_model.state_dict().items():
            # 生成一个和参数矩阵大小相同的0矩阵
            diff[name] = torch.zeros_like(params)
            diff_fake[name] = torch.zeros_like(params)
        for name, data in self.local_model.state_dict().items():
            # 计算训练后与训练前的差值
            # sign4 = torch.zeros_like(data)
            # print(data)
            # print(model.state_dict()[name])
            # sign1 = data.gt(0)
            # gt函数是大于0的意思
            # sign2 = model.state_dict()[name].gt(0)
            # sign3 = sign1.eq(sign2)
            # print(sign1)
            # print(sign2)
            # print(sign3)
            # diff[name] = (data - model.state_dict()[name])
            # sign5 = torch.where(sign3, diff[name], sign4)
            # diff[name] = sign5
            # print(sign5)
            # print("Client %d local train done" % self.client_id)
            diff[name] = (data - model.state_dict()[name])


            #    if torch.eq(diff[name], 0):
            #        xdata = [diffall[name], diff[name]]
            #        ydata = [accall[0], accall[1]]
            #       x_arr = np.array(xdata)
            #       y_arr = np.array(ydata)

            #       popt, pcov = curve_fit(sigmoid, x_arr, y_arr, method='dogbox')

            # yvals1 = sigmoid(8, popt[0], popt[1])
            # print("一阶拟合数据为: ", yvals1)

            # yvals2 = inverse(0.928328893092818, popt[0], popt[1])
            # print("一阶拟合数据为: ", yvals2)
            #      diff[name] = inverse(0.9, popt[0], popt[1])

        diff = FakeData.generate_fake_gradients(diff)
        diff = {key: value.to("cuda") for key, value in diff.items()}
        # print("移动到GPU后的设备:", next(iter(diff.values())).device)
        # 客户端返回差值
        return diff

    # 反转攻击
    def local_train_fake2(self, model):
        for name, param in model.state_dict().items():
            # 加载全局模型参数到本地模型中
            self.local_model.state_dict()[name].copy_(param.clone())

        # 定义最优化函数器用户本地模型训练
        optimizer = torch.optim.SGD(
            self.local_model.parameters(),
            lr=self.conf['lr'],
            momentum=self.conf['momentum']
        )

        # 本地训练模型
        self.local_model.train()
        for e in range(self.conf["local_epochs"]):
            for batch_id, batch in enumerate(self.train_loader):
                data, target = batch

                # 将数据转换为 float 类型（这一步很重要）
                data = data.float()  # Convert to float32 type
                target = target.long()  # Ensure target is long for classification

                # 如果可以的话加载到 GPU
                if torch.cuda.is_available():
                    data = data.cuda()
                    target = target.cuda()

                optimizer.zero_grad()
                output = self.local_model(data)
                loss = torch.nn.functional.cross_entropy(output, target)
                loss.backward()
                optimizer.step()
        # 计算并返回差值
        diff = dict()
        for name, data in self.local_model.state_dict().items():
            diff[name] = torch.flip(data, dims=[0])
        return diff

