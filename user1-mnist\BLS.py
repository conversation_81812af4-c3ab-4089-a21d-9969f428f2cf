"""
BLS短签名算法实现 - 2-n签名方案
从n个设备中随机选择c和d作为部分签名者
对消息体H(P,i)进行签名和验证
"""

import hashlib
import random
from typing import Tuple, List, Optional
import time


class Device:
    """签名设备"""

    def __init__(self, device_id: str, private_key: int):
        self.device_id = device_id
        self.private_key = private_key
        self.public_key = None  # 将由BLS系统计算

    def __str__(self):
        return f"设备ID: {self.device_id}"


class PartialSignature:
    """部分签名"""

    def __init__(self, device_id: str, signature: int, message_hash: int):
        self.device_id = device_id
        self.signature = signature
        self.message_hash = message_hash
        self.timestamp = time.time()

    def __str__(self):
        return f"部分签名[{self.device_id}]: {self.signature}"


class BLSShortSignature:
    """BLS短签名系统 - 2-n签名方案"""

    def __init__(self, n_devices: int = 5):
        # 系统参数
        self.p = 2**31 - 1  # 大素数（简化）
        self.g = 2  # 生成元
        self.n_devices = n_devices
        self.devices = {}  # device_id -> Device
        self.setup_devices()

    def setup_devices(self):
        """初始化n个设备"""
        print(f"初始化 {self.n_devices} 个签名设备...")
        for i in range(self.n_devices):
            device_id = f"Device_{i+1}"
            private_key = random.randint(1, self.p - 1)
            device = Device(device_id, private_key)
            device.public_key = pow(self.g, private_key, self.p)
            self.devices[device_id] = device
            print(f"设备 {device_id} 初始化完成，公钥: {device.public_key}")

    def hash_message(self, message: bytes, participant_id: str, index: int) -> int:
        """计算消息哈希 H(P,i)"""
        # 构造哈希输入：消息 + 参与者ID + 索引
        hash_input = message + participant_id.encode() + str(index).encode()
        hash_value = hashlib.sha256(hash_input).digest()
        return int.from_bytes(hash_value, 'big') % self.p

    def select_signers(self, num_signers: int = 2) -> List[str]:
        """从n个设备中随机选择签名者（默认选择2个：c和d）"""
        device_ids = list(self.devices.keys())
        selected = random.sample(device_ids, min(num_signers, len(device_ids)))
        print(f"随机选择的签名设备: {selected}")
        return selected

    def generate_partial_signature(self, device_id: str, message: bytes, index: int) -> PartialSignature:
        """生成部分签名"""
        if device_id not in self.devices:
            raise ValueError(f"设备 {device_id} 不存在")

        device = self.devices[device_id]

        # 计算消息哈希 H(P,i)
        message_hash = self.hash_message(message, device_id, index)

        # 生成部分签名：sig = H(P,i)^private_key mod p
        signature = pow(message_hash, device.private_key, self.p)

        return PartialSignature(device_id, signature, message_hash)

    def aggregate_signatures(self, partial_signatures: List[PartialSignature]) -> int:
        """聚合部分签名"""
        if not partial_signatures:
            return 0

        # 简单的签名聚合：所有部分签名相乘
        aggregated = 1
        for ps in partial_signatures:
            aggregated = (aggregated * ps.signature) % self.p

        return aggregated

    def verify_partial_signature(self, partial_sig: PartialSignature, message: bytes, index: int) -> bool:
        """验证部分签名"""
        device = self.devices[partial_sig.device_id]

        # 重新计算消息哈希
        expected_hash = self.hash_message(message, partial_sig.device_id, index)

        # 验证：g^sig = (g^private_key)^H(P,i) = public_key^H(P,i) mod p
        left = pow(self.g, partial_sig.signature, self.p)
        right = pow(device.public_key, expected_hash, self.p)

        return left == right and expected_hash == partial_sig.message_hash

    def verify_aggregated_signature(self, aggregated_sig: int, partial_signatures: List[PartialSignature],
                                  message: bytes, index: int) -> bool:
        """验证聚合签名"""
        # 计算预期的聚合签名
        expected_aggregated = self.aggregate_signatures(partial_signatures)

        # 验证每个部分签名
        for ps in partial_signatures:
            if not self.verify_partial_signature(ps, message, index):
                return False

        return aggregated_sig == expected_aggregated
    
def demo_2n_signature():
    """2-n签名方案演示"""
    print("=== BLS短签名 2-n方案演示 ===\n")

    # 创建BLS短签名系统（5个设备）
    bls = BLSShortSignature(n_devices=5)
    print()

    # 测试消息
    message = b"Important transaction data"
    index = 1
    print(f"待签名消息: {message.decode()}")
    print(f"消息索引: {index}")
    print()

    # 第一步：随机选择2个签名者（c和d）
    print("第一步：随机选择签名设备...")
    selected_signers = bls.select_signers(num_signers=2)
    signer_c, signer_d = selected_signers[0], selected_signers[1]
    print(f"选择的签名者: c={signer_c}, d={signer_d}")
    print()

    # 第二步：生成部分签名
    print("第二步：生成部分签名...")
    partial_signatures = []

    # 设备c生成部分签名
    print(f"设备 {signer_c} 对 H(P,{index}) 进行签名...")
    ps_c = bls.generate_partial_signature(signer_c, message, index)
    partial_signatures.append(ps_c)
    print(f"设备 {signer_c} 部分签名: {ps_c.signature}")
    print(f"消息哈希 H(P,{index}): {ps_c.message_hash}")

    # 设备d生成部分签名
    print(f"设备 {signer_d} 对 H(P,{index}) 进行签名...")
    ps_d = bls.generate_partial_signature(signer_d, message, index)
    partial_signatures.append(ps_d)
    print(f"设备 {signer_d} 部分签名: {ps_d.signature}")
    print(f"消息哈希 H(P,{index}): {ps_d.message_hash}")
    print()

    # 第三步：验证部分签名
    print("第三步：验证部分签名...")
    for ps in partial_signatures:
        is_valid = bls.verify_partial_signature(ps, message, index)
        print(f"设备 {ps.device_id} 部分签名验证: {'✓ 有效' if is_valid else '✗ 无效'}")
    print()

    # 第四步：聚合签名
    print("第四步：聚合部分签名...")
    aggregated_signature = bls.aggregate_signatures(partial_signatures)
    print(f"聚合签名: {aggregated_signature}")
    print()

    # 第五步：验证聚合签名
    print("第五步：验证聚合签名...")
    is_aggregated_valid = bls.verify_aggregated_signature(
        aggregated_signature, partial_signatures, message, index
    )
    print(f"聚合签名验证: {'✓ 有效' if is_aggregated_valid else '✗ 无效'}")
    print()

    # 显示系统信息
    print("系统信息:")
    print(f"总设备数: {bls.n_devices}")
    print(f"参与签名设备数: {len(partial_signatures)}")
    print(f"签名方案: 2-{bls.n_devices} (从{bls.n_devices}个设备中选择2个)")
    print(f"系统素数p: {bls.p}")
    print(f"生成元g: {bls.g}")

    return bls, partial_signatures, aggregated_signature


if __name__ == "__main__":
    demo_2n_signature()
