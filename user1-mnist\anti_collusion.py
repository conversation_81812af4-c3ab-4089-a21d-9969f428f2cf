"""
防勾结机制 - 防止DPC与GW勾结的安全方案
包含多重网关、门限签名、零知识证明等机制
"""

import hashlib
import random
import time
from typing import Dict, List, Tuple, Optional
import numpy as np
from Paillier import PaillierCryptosystem, PaillierPublicKey, PaillierPrivateKey


class ThresholdSignature:
    """门限签名系统 - 防止单点勾结"""
    
    def __init__(self, n: int, t: int):
        """
        n: 总参与者数量
        t: 门限值（需要至少t个参与者才能重构签名）
        """
        self.n = n
        self.t = t
        self.participants = {}
        self.shares = {}  # 密钥分片
        
    def generate_shares(self, secret: int) -> Dict[int, int]:
        """使用Shamir秘密分享生成密钥分片"""
        # 简化的Shamir秘密分享实现
        coefficients = [secret] + [random.randint(1, 2**31-1) for _ in range(self.t-1)]
        
        shares = {}
        for i in range(1, self.n + 1):
            share = sum(coeff * (i ** j) for j, coeff in enumerate(coefficients)) % (2**31-1)
            shares[i] = share
            
        return shares
    
    def reconstruct_secret(self, shares: Dict[int, int]) -> int:
        """重构秘密（需要至少t个分片）"""
        if len(shares) < self.t:
            raise ValueError(f"需要至少{self.t}个分片，但只有{len(shares)}个")
        
        # 拉格朗日插值重构
        secret = 0
        participants = list(shares.keys())[:self.t]
        
        for i in participants:
            numerator = 1
            denominator = 1
            
            for j in participants:
                if i != j:
                    numerator *= (0 - j)
                    denominator *= (i - j)
            
            lagrange_coeff = numerator // denominator
            secret += shares[i] * lagrange_coeff
            
        return secret % (2**31-1)


class MultiGatewaySystem:
    """多重网关系统 - 防止单个GW作恶"""
    
    def __init__(self, num_gateways: int = 3):
        self.num_gateways = num_gateways
        self.gateways = {}
        self.threshold = (num_gateways // 2) + 1  # 需要过半数同意
        self.aggregation_results = {}
        
        # 初始化多个网关
        for i in range(num_gateways):
            gw_id = f"GW_{i+1}"
            self.gateways[gw_id] = {
                'id': gw_id,
                'received_data': [],
                'aggregation_result': None,
                'is_honest': True  # 假设初始都是诚实的
            }
    
    def distribute_data_to_gateways(self, encrypted_data: int, signature: int, md_id: str):
        """将MD数据分发给所有网关"""
        for gw_id, gateway in self.gateways.items():
            gateway['received_data'].append({
                'encrypted_data': encrypted_data,
                'signature': signature,
                'md_id': md_id,
                'timestamp': time.time()
            })
            print(f"数据已分发给 {gw_id}")
    
    def parallel_aggregation(self):
        """所有网关并行进行数据聚合"""
        for gw_id, gateway in self.gateways.items():
            # 每个网关独立聚合数据
            aggregated_data = 0
            for data_item in gateway['received_data']:
                aggregated_data += data_item['encrypted_data']
            
            gateway['aggregation_result'] = aggregated_data
            print(f"{gw_id} 聚合结果: {aggregated_data}")
    
    def consensus_verification(self) -> Tuple[bool, int]:
        """共识验证 - 检查网关结果是否一致"""
        results = [gw['aggregation_result'] for gw in self.gateways.values()]
        
        # 统计结果分布
        result_counts = {}
        for result in results:
            result_counts[result] = result_counts.get(result, 0) + 1
        
        # 找到多数派结果
        majority_result = max(result_counts.items(), key=lambda x: x[1])
        majority_count = majority_result[1]
        consensus_result = majority_result[0]
        
        # 检查是否达到门限
        consensus_reached = majority_count >= self.threshold
        
        if consensus_reached:
            print(f"✓ 达成共识，聚合结果: {consensus_result} (支持数: {majority_count}/{self.num_gateways})")
        else:
            print(f"✗ 未达成共识，最高支持数: {majority_count}/{self.num_gateways}")
            
        # 标记可能作恶的网关
        for gw_id, gateway in self.gateways.items():
            if gateway['aggregation_result'] != consensus_result:
                gateway['is_honest'] = False
                print(f"⚠️ 网关 {gw_id} 结果异常，可能被攻击")
        
        return consensus_reached, consensus_result


class ZeroKnowledgeProof:
    """零知识证明 - 让DPC验证数据正确性但看不到原始数据"""
    
    def __init__(self):
        self.commitment_scheme = {}
    
    def generate_commitment(self, value: int, randomness: int) -> int:
        """生成承诺值 C = g^value * h^randomness mod p"""
        g = 2
        h = 3
        p = 2**31 - 1
        
        commitment = (pow(g, value, p) * pow(h, randomness, p)) % p
        return commitment
    
    def generate_proof(self, value: int, randomness: int, commitment: int) -> Dict:
        """生成零知识证明"""
        # 简化的Schnorr证明
        r = random.randint(1, 2**30)
        g = 2
        h = 3
        p = 2**31 - 1
        
        # 第一轮
        a = (pow(g, r, p) * pow(h, r, p)) % p
        
        # 挑战
        challenge = int(hashlib.sha256(f"{commitment}{a}".encode()).hexdigest(), 16) % (2**30)
        
        # 响应
        z_value = (r + challenge * value) % (2**30)
        z_rand = (r + challenge * randomness) % (2**30)
        
        return {
            'commitment': commitment,
            'challenge': challenge,
            'z_value': z_value,
            'z_rand': z_rand,
            'a': a
        }
    
    def verify_proof(self, proof: Dict) -> bool:
        """验证零知识证明"""
        g = 2
        h = 3
        p = 2**31 - 1
        
        # 重新计算
        left = (pow(g, proof['z_value'], p) * pow(h, proof['z_rand'], p)) % p
        right = (proof['a'] * pow(proof['commitment'], proof['challenge'], p)) % p
        
        return left == right


class SecurePaillier:
    """增强版Paillier加密 - 提高安全性"""
    
    def __init__(self, key_size: int = 2048):  # 增加密钥长度
        self.key_size = key_size
        self.paillier_system = PaillierCryptosystem(key_size=key_size)
        self.public_key, self.private_key = self.paillier_system.generate_keypair()
        
    def enhanced_encrypt(self, plaintext: int, use_blinding: bool = True) -> Tuple[int, Dict]:
        """增强加密 - 添加盲化因子"""
        # 基础Paillier加密
        ciphertext = self.public_key.encrypt(plaintext)
        
        metadata = {
            'timestamp': time.time(),
            'key_size': self.key_size,
            'blinding_used': use_blinding
        }
        
        if use_blinding:
            # 添加随机盲化因子
            blinding_factor = random.randint(1, self.public_key.n - 1)
            blinded_ciphertext = self.public_key.add_encrypted(
                ciphertext, 
                self.public_key.encrypt(0, blinding_factor)
            )
            metadata['blinded'] = True
            return blinded_ciphertext, metadata
        
        return ciphertext, metadata
    
    def batch_encrypt_with_proof(self, plaintexts: List[int]) -> Tuple[List[int], Dict]:
        """批量加密并生成正确性证明"""
        ciphertexts = []
        total_plaintext = sum(plaintexts)
        
        # 加密每个明文
        for pt in plaintexts:
            ct, _ = self.enhanced_encrypt(pt)
            ciphertexts.append(ct)
        
        # 生成总和的证明
        zkp = ZeroKnowledgeProof()
        randomness = random.randint(1, 2**30)
        commitment = zkp.generate_commitment(total_plaintext, randomness)
        proof = zkp.generate_proof(total_plaintext, randomness, commitment)
        
        return ciphertexts, {
            'proof': proof,
            'total_commitment': commitment,
            'batch_size': len(plaintexts)
        }


class AntiCollusionDPC:
    """防勾结数据处理中心"""
    
    def __init__(self):
        self.entity_name = "AntiCollusionDPC"
        self.multi_gw_system = MultiGatewaySystem(num_gateways=3)
        self.threshold_sig = ThresholdSignature(n=5, t=3)
        self.zkp_verifier = ZeroKnowledgeProof()
        self.verification_history = []
        
    def receive_from_multiple_gateways(self, gw_results: Dict[str, Tuple[int, Dict]]):
        """从多个网关接收数据并进行交叉验证"""
        print(f"[{self.entity_name}] 从 {len(gw_results)} 个网关接收数据")
        
        # 提取聚合结果
        aggregation_results = {}
        for gw_id, (result, metadata) in gw_results.items():
            aggregation_results[gw_id] = result
        
        # 检查结果一致性
        unique_results = set(aggregation_results.values())
        if len(unique_results) == 1:
            print("✓ 所有网关结果一致")
            consensus_result = list(unique_results)[0]
            is_valid = True
        else:
            print("⚠️ 网关结果不一致，可能存在勾结")
            # 使用多数投票
            result_counts = {}
            for result in aggregation_results.values():
                result_counts[result] = result_counts.get(result, 0) + 1
            
            majority_result = max(result_counts.items(), key=lambda x: x[1])
            consensus_result = majority_result[0]
            is_valid = majority_result[1] >= 2  # 至少2个网关同意
        
        # 记录验证历史
        self.verification_history.append({
            'timestamp': time.time(),
            'gw_results': aggregation_results,
            'consensus_result': consensus_result,
            'is_valid': is_valid
        })
        
        return is_valid, consensus_result
    
    def verify_with_zero_knowledge(self, proof: Dict) -> bool:
        """使用零知识证明验证数据正确性"""
        return self.zkp_verifier.verify_proof(proof)
    
    def detect_collusion_pattern(self) -> List[str]:
        """检测勾结模式"""
        suspicious_gateways = []
        
        if len(self.verification_history) < 3:
            return suspicious_gateways
        
        # 分析最近的验证历史
        recent_history = self.verification_history[-5:]
        
        # 统计每个网关的异常行为
        gw_anomaly_count = {}
        for record in recent_history:
            if not record['is_valid']:
                for gw_id, result in record['gw_results'].items():
                    if result != record['consensus_result']:
                        gw_anomaly_count[gw_id] = gw_anomaly_count.get(gw_id, 0) + 1
        
        # 标记异常频率高的网关
        for gw_id, anomaly_count in gw_anomaly_count.items():
            if anomaly_count >= 2:  # 连续2次异常
                suspicious_gateways.append(gw_id)
        
        return suspicious_gateways


def demo_anti_collusion():
    """防勾结机制演示"""
    print("=== 防勾结机制演示 ===\n")
    
    # 初始化防勾结系统
    anti_collusion_dpc = AntiCollusionDPC()
    secure_paillier = SecurePaillier(key_size=1024)
    
    print("1. 模拟MD数据加密和分发")
    md_data = [10, 20, 30, 40, 50]
    
    # 使用增强Paillier加密
    encrypted_data, proof_data = secure_paillier.batch_encrypt_with_proof(md_data)
    print(f"加密数据: {len(encrypted_data)} 个密文")
    print(f"零知识证明已生成")
    
    print("\n2. 多网关并行聚合")
    multi_gw = anti_collusion_dpc.multi_gw_system
    
    # 分发数据到所有网关
    for i, ct in enumerate(encrypted_data):
        multi_gw.distribute_data_to_gateways(ct, hash(str(ct)), f"MD_{i+1}")
    
    # 并行聚合
    multi_gw.parallel_aggregation()
    
    # 共识验证
    consensus_reached, consensus_result = multi_gw.consensus_verification()
    
    print("\n3. DPC交叉验证")
    gw_results = {}
    for gw_id, gateway in multi_gw.gateways.items():
        gw_results[gw_id] = (gateway['aggregation_result'], {'timestamp': time.time()})
    
    is_valid, final_result = anti_collusion_dpc.receive_from_multiple_gateways(gw_results)
    
    print(f"\n最终验证结果: {'✓ 有效' if is_valid else '✗ 无效'}")
    print(f"聚合结果: {final_result}")
    
    # 零知识证明验证
    zkp_valid = anti_collusion_dpc.verify_with_zero_knowledge(proof_data['proof'])
    print(f"零知识证明验证: {'✓ 有效' if zkp_valid else '✗ 无效'}")
    
    print("\n4. 模拟勾结攻击")
    # 模拟GW_1被攻击
    multi_gw.gateways['GW_1']['aggregation_result'] = 999999  # 恶意结果
    multi_gw.gateways['GW_1']['is_honest'] = False
    
    print("GW_1 被攻击，提供恶意结果...")
    
    # 重新验证
    gw_results_attacked = {}
    for gw_id, gateway in multi_gw.gateways.items():
        gw_results_attacked[gw_id] = (gateway['aggregation_result'], {'timestamp': time.time()})
    
    is_valid_after_attack, result_after_attack = anti_collusion_dpc.receive_from_multiple_gateways(gw_results_attacked)
    
    print(f"攻击后验证结果: {'✓ 有效' if is_valid_after_attack else '✗ 无效'}")
    
    # 检测勾结模式
    suspicious_gws = anti_collusion_dpc.detect_collusion_pattern()
    if suspicious_gws:
        print(f"⚠️ 检测到可疑网关: {suspicious_gws}")
    
    print("\n=== 防勾结机制演示完成 ===")


if __name__ == "__main__":
    demo_anti_collusion()
